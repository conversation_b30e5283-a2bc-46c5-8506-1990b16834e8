import * as Sentry from '@sentry/node'

interface SentryContext {
  user?: {
    id?: string
    username?: string
    email?: string
    [key: string]: any
  }
  tags?: Record<string, string>
  extra?: Record<string, any>
  level?: Sentry.SeverityLevel
  fingerprint?: string[]
}

class SentryLogger {
  private static autoReport = {
    error: true,    // error级别自动上报
    warn: false,    // warn级别默认不自动上报
    info: false,
    debug: false,
    trace: false
  }

  // 检测服务名的静态方法
  private static detectServiceName(): string {
    // 优先使用环境变量
    if (process.env.SERVICE_NAME) {
      return process.env.SERVICE_NAME
    }

    // 从进程路径推断服务名 - 自动从 apps 目录检测
    const cwd = process.cwd()

    // 匹配 /apps/service_name/ 模式
    const appsMatch = cwd.match(/\/apps\/([^/]+)/)
    if (appsMatch) {
      return appsMatch[1]
    }

    // 如果不在 apps 目录下，检查是否在根目录的特定服务目录
    if (cwd.includes('packages')) {
      return 'packages' // 运行在 packages 目录下的通用服务
    }

    return 'unknown'
  }

  // 设置全局上下文（用户信息、服务标签等）
  public static setContext(context: SentryContext) {
    // Sentry v8+ 新API
    if (context.user) {
      Sentry.setUser(context.user)
    }
    if (context.tags) {
      Object.entries(context.tags).forEach(([key, value]) => {
        Sentry.setTag(key, value)
      })
    }
    if (context.extra) {
      Object.entries(context.extra).forEach(([key, value]) => {
        Sentry.setContext(key, value)
      })
    }
  }

  // 手动上报错误
  public static captureError(error: Error, context?: SentryContext) {
    return Sentry.withScope((scope) => {
      this.applyScopeContext(scope, context)
      return Sentry.captureException(error)
    })
  }

  // 手动上报消息
  public static captureMessage(message: string, context?: SentryContext) {
    return Sentry.withScope((scope) => {
      this.applyScopeContext(scope, context)
      return Sentry.captureMessage(message, context?.level || 'info')
    })
  }

  // 业务错误上报（预定义的错误类型）
  public static reportBusinessError(errorType: string, details: any, context?: SentryContext) {
    const error = new Error(`Business Error: ${errorType}`)
    error.name = 'BusinessError'

    return Sentry.withScope((scope) => {
      scope.setTag('errorType', errorType)
      scope.setTag('category', 'business')
      scope.setContext('errorDetails', details)
      this.applyScopeContext(scope, context)
      return Sentry.captureException(error)
    })
  }

  // 性能问题上报
  public static reportPerformanceIssue(operation: string, duration: number, threshold: number, context?: SentryContext) {
    return Sentry.withScope((scope) => {
      scope.setTag('category', 'performance')
      scope.setTag('operation', operation)
      scope.setLevel('warning')
      scope.setContext('performance', { duration, threshold, exceeded: duration > threshold })
      this.applyScopeContext(scope, context)
      return Sentry.captureMessage(`Performance issue detected: ${operation} took ${duration}ms (threshold: ${threshold}ms)`)
    })
  }

  // 第三方API错误上报
  public static reportApiError(apiName: string, status: number, response?: any, context?: SentryContext) {
    return Sentry.withScope((scope) => {
      scope.setTag('category', 'api')
      scope.setTag('apiName', apiName)
      scope.setTag('statusCode', status.toString())
      scope.setContext('apiResponse', { status, response })
      this.applyScopeContext(scope, context)
      return Sentry.captureMessage(`API Error: ${apiName} returned ${status}`, 'error')
    })
  }

  // 数据库错误上报
  public static reportDatabaseError(operation: string, error: Error, context?: SentryContext) {
    return Sentry.withScope((scope) => {
      scope.setTag('category', 'database')
      scope.setTag('operation', operation)
      scope.setContext('databaseError', {
        message: error.message,
        stack: error.stack
      })
      this.applyScopeContext(scope, context)
      return Sentry.captureException(error)
    })
  }

  // 用户行为异常上报
  public static reportUserBehaviorAnomaly(userId: string, behaviorType: string, details: any, context?: SentryContext) {
    return Sentry.withScope((scope) => {
      scope.setUser({ id: userId })
      scope.setTag('category', 'user_behavior')
      scope.setTag('behaviorType', behaviorType)
      scope.setLevel('warning')
      scope.setContext('behaviorDetails', details)
      this.applyScopeContext(scope, context)
      return Sentry.captureMessage(`User behavior anomaly: ${behaviorType} for user ${userId}`)
    })
  }

  private static applyScopeContext(scope: Sentry.Scope, context?: SentryContext) {
    if (!context) return

    if (context.user) {
      scope.setUser(context.user)
    }
    if (context.tags) {
      Object.entries(context.tags).forEach(([key, value]) => {
        scope.setTag(key, value)
      })
    }
    if (context.extra) {
      Object.entries(context.extra).forEach(([key, value]) => {
        scope.setContext(key, value)
      })
    }
    if (context.level) {
      scope.setLevel(context.level)
    }
    if (context.fingerprint) {
      scope.setFingerprint(context.fingerprint)
    }
  }

  // 内部方法：自动上报逻辑
  public static _autoReport(level: string, args: any[]) {
    // 检查是否需要自动上报
    if (!this.autoReport[level as keyof typeof this.autoReport]) return

    // 检查第一个参数是否是对象（pino格式：logger.error({key: value}, 'message', ...args)）
    let contextObject: any = null
    let messageArgs = args
    if (args.length > 0 && typeof args[0] === 'object' && args[0] !== null && !(args[0] instanceof Error)) {
      contextObject = args[0]
      messageArgs = args.slice(1) // 去掉第一个对象参数
    }

    // 处理Error对象
    const errors = messageArgs.filter((arg) => arg instanceof Error)
    if (errors.length > 0) {
      errors.forEach((originalError) => {
        // 创建一个新Error来捕获当前调用堆栈，但保留原Error的消息
        const errorWithCallStack = new Error(originalError.message)
        errorWithCallStack.name = originalError.name || 'Error'

        // 清理堆栈，移除logger内部调用
        if (errorWithCallStack.stack) {
          const stackLines = errorWithCallStack.stack.split('\n')
          const filteredStack = stackLines.filter((line, index) => {
            if (index === 0) return true // 保留错误消息行
            return !line.includes('logger.ts:') &&
                   !line.includes('sentryLogger.ts:') &&
                   !line.includes('_autoReport')
          })
          errorWithCallStack.stack = filteredStack.join('\n')
        }

        Sentry.withScope((scope) => {
          scope.setLevel(level as Sentry.SeverityLevel)
          scope.setTag('source', 'logger_auto_report')

          // 保存原始Error的详细信息作为上下文
          scope.setContext('originalError', {
            name: originalError.name,
            message: originalError.message,
            stack: originalError.stack
          })

          // 添加logger调用时的上下文对象
          if (contextObject) {
            scope.setContext('loggerContext', contextObject)

            // 如果有chat_id，设置为用户标识（便于Sentry中串联查看）
            if (contextObject.chat_id) {
              // 检测服务名
              const serviceName = this.detectServiceName()

              scope.setUser({
                id: contextObject.chat_id,
                username: `${serviceName}_${contextObject.chat_id}`
              })
              scope.setTag('chat_id', contextObject.chat_id)
            }

            // 如果有component，设置为标签
            if (contextObject.component) {
              scope.setTag('component', contextObject.component)
            }
          }

          // 添加额外的上下文信息
          const nonErrorArgs = messageArgs.filter((arg) => !(arg instanceof Error))
          if (nonErrorArgs.length > 0) {
            scope.setContext('additionalArgs', {
              args: nonErrorArgs.map((arg) =>
                typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
              )
            })
          }

          Sentry.captureException(errorWithCallStack)
        })
      })
      return
    }

    // 处理普通消息 - 创建一个新的Error对象以获得准确的堆栈信息
    const message = messageArgs.map((arg) =>
      typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
    ).join(' ')

    if (message) {
      // 创建一个Error对象来捕获调用堆栈
      const syntheticError = new Error(message)
      syntheticError.name = 'LoggerAutoReport'

      // 清理堆栈，移除logger内部的调用层级
      if (syntheticError.stack) {
        const stackLines = syntheticError.stack.split('\n')
        const filteredStack = stackLines.filter((line, index) => {
          if (index === 0) return true // 保留错误消息行
          // 过滤掉logger内部的调用
          return !line.includes('logger.ts:') &&
                 !line.includes('sentryLogger.ts:') &&
                 !line.includes('_autoReport')
        })
        syntheticError.stack = filteredStack.join('\n')
      }

      Sentry.withScope((scope) => {
        scope.setLevel(level as Sentry.SeverityLevel)
        scope.setTag('source', 'logger_auto_report')
        scope.setTag('synthetic', 'true')

        // 添加logger调用时的上下文对象
        if (contextObject) {
          scope.setContext('loggerContext', contextObject)

          // 如果有chat_id，设置为用户标识（便于Sentry中串联查看）
          if (contextObject.chat_id) {
            // 检测服务名
            const serviceName = this.detectServiceName()

            scope.setUser({
              id: contextObject.chat_id,
              username: `${serviceName}_${contextObject.chat_id}`
            })
            scope.setTag('chat_id', contextObject.chat_id)
          }

          // 如果有component，设置为标签
          if (contextObject.component) {
            scope.setTag('component', contextObject.component)
          }
        }

        Sentry.captureException(syntheticError)
      })
    }
  }

  // 设置自动上报配置
  public static setAutoReportConfig(config: Partial<typeof SentryLogger.autoReport>) {
    Object.assign(this.autoReport, config)
  }
}

export default SentryLogger