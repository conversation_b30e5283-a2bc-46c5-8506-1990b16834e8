import * as Sentry from '@sentry/node'

interface SentryInitOptions {
  serviceName?: string
  environment?: string
  dsn?: string
  tracesSampleRate?: number
  profilesSampleRate?: number
}

class SentryInitializer {
  private static isInitialized = false

  /**
   * 初始化Sentry
   * 支持通过环境变量或参数配置
   */
  public static init(options: SentryInitOptions = {}) {
    // 避免重复初始化
    if (this.isInitialized) {
      return
    }
    const dsn = 'https://<EMAIL>/4509982481186816'
    const environment = options.environment || process.env.NODE_ENV || 'development'
    const serviceName = options.serviceName || this.detectServiceName()

    // 如果没有配置DSN，跳过初始化
    if (!dsn) {
      console.debug('SENTRY_DSN未配置，跳过Sentry初始化')
      return
    }

    try {
      Sentry.init({
        dsn,
        // 性能监控（不包含CPU分析）
        tracesSampleRate: options.tracesSampleRate ?? 1.0,

        environment,

        // 全局标签设置
        beforeSend: (event) => {
          // 为每个事件添加服务标签
          if (!event.tags) {
            event.tags = {}
          }
          event.tags.service = serviceName

          // 添加时间戳
          event.tags.timestamp = new Date().toISOString()

          return event
        },

        // 错误过滤
        beforeSendTransaction: (transaction) => {
          // 可以在这里过滤不需要的事务
          return transaction
        }
      })

      this.isInitialized = true
      console.log(`Sentry初始化成功 - 服务: ${serviceName}, 环境: ${environment}`)

      // 设置全局标签（Sentry v8+ 新API）
      Sentry.setTag('service', serviceName)
      Sentry.setTag('environment', environment)

    } catch (error) {
      console.error('Sentry初始化失败:', error)
    }
  }

  /**
   * 自动检测服务名称
   * 根据进程启动路径或环境变量推断
   */
  private static detectServiceName(): string {
    // 优先使用环境变量
    if (process.env.SERVICE_NAME) {
      return process.env.SERVICE_NAME
    }

    // 从进程路径推断服务名 - 自动从 apps 目录检测
    const cwd = process.cwd()

    // 匹配 /apps/service_name/ 模式
    const appsMatch = cwd.match(/\/apps\/([^/]+)/)
    if (appsMatch) {
      return appsMatch[1]
    }

    // 如果不在 apps 目录下，检查是否在根目录的特定服务目录
    if (cwd.includes('packages')) {
      return 'packages' // 运行在 packages 目录下的通用服务
    }

    return 'unknown'
  }

  /**
   * 检查是否已初始化
   */
  public static get initialized(): boolean {
    return this.isInitialized
  }

  /**
   * 手动设置服务上下文
   */
  public static setServiceContext(serviceName: string, additionalTags?: Record<string, string>) {
    if (!this.isInitialized) {
      console.warn('Sentry未初始化，无法设置服务上下文')
      return
    }

    // Sentry v8+ 新API
    Sentry.setTag('service', serviceName)

    if (additionalTags) {
      Object.entries(additionalTags).forEach(([key, value]) => {
        Sentry.setTag(key, value)
      })
    }
  }
}

export default SentryInitializer