import logger from 'model/logger/logger'
import { ChatDB, IChat } from '../database/chat'
import { ChatStateStore } from '../local_cache/chat_state_store'
import { Config } from 'config'
import { EventTracker, IEventType } from 'model/logger/data_driven'
import { JuziAPI } from 'model/juzi/api'
import { IWecomMsgType } from 'model/juzi/type'

export const HumanTransferType = {
  // general
  UnknownMessageType: '客户发了一个文件',
  NotBindPhone: '客户手机号绑定失败',
  ProblemSolving: '客户遇到问题',
  FailedToJoinGroup: '客户拉群失败',
  ProcessImage: '客户发了一张【图片】',
  MessageSendFailed: '消息发送失败',
  ReaskAnotherDay: '客户次日被主动提醒支付',
  HesitatePayment: '客户支付犹豫',
  VoiceOrVideoCall: '客户发起语音/视频通话',
  RobotDetected: '客户识别到了AI',
  PaidCourse: '[烟花]客户已支付[烟花]',
  ProcessVideo: '客户发了一个【视频】',
  ProcessVideoFailed: '客户发了一个【视频】，识别失败',

  // moer_overseas

  // yuhe
  ExecutePostpone: '客户已延期',
  ExecuteRetrain: '客户已复训',

  // haogu

} as const

/**
 * 通知发送提供器接口。允许通过不同实现发送通知（默认使用 JuziAPI）。
 */
export interface NotificationProvider {
    notify: (message: string, options?: NotifyOptions) => Promise<void>
}

/**
 * 通知可选参数。
 */
export interface NotifyOptions {
    imBotId?: string
    groupId?: string
}

/**
 * 默认通知实现：通过 JuziAPI 发送群消息。
 */
export class JuziNotificationProvider implements NotificationProvider {
  async notify(message: string, options?: NotifyOptions): Promise<void> {
    const imBotId = options?.imBotId ?? (Config.setting.wechatConfig?.id as string)
    const groupId = options?.groupId ?? (Config.setting.wechatConfig?.notifyGroupId as string)

    await JuziAPI.sendGroupMsg(imBotId, groupId, {
      type: IWecomMsgType.Text,
      text: message
    })
  }
}

// 删除 BaseHumanTransfer，合并能力到下方 HumanTransfer 单一类中

export type TransferKey = string | number

export interface HumanTransferParams<T extends TransferKey> {
    transferMessage: Record<T, string>
    eventTracker: EventTracker
    chatDB: ChatDB<IChat>
    chatStateStore: ChatStateStore
    notifier?: NotificationProvider
}

export type TransferMode = true | false | 'onlyNotify'


export class HumanTransfer<T extends TransferKey> {
  private readonly transferMessage: Record<T, string>
  private readonly eventTracker: EventTracker
  private readonly chatDBClient: ChatDB<IChat>
  private readonly chatStateStoreClient: ChatStateStore
  private readonly notifier: NotificationProvider

  constructor(params: HumanTransferParams<T>) {
    this.transferMessage = params.transferMessage
    this.eventTracker = params.eventTracker
    this.chatDBClient = params.chatDB
    this.chatStateStoreClient = params.chatStateStore
    this.notifier = params.notifier ?? new JuziNotificationProvider()
  }

  // 方式一：通过类型键转文案并埋点
  public async transfer(
    chatId: string,
    userId: string,
    transferType: T | string,
    toHuman: TransferMode = true,
    additionalMsg?: string,
    options?: NotifyOptions & { notifyFunction?: () => Promise<void>, imBotId?: string }
  ) {
    if (userId === 'null') {
      logger.error('[HumanTransfer] userId is null', transferType)
      return
    }

    const keyOrMessage = transferType as any
    const resolved = (typeof keyOrMessage === 'string' || typeof keyOrMessage === 'number') && (this.transferMessage as any)[keyOrMessage]
      ? (this.transferMessage as any)[keyOrMessage]
      : String(keyOrMessage)
    this.eventTracker.track(chatId, IEventType.TransferToManual, { reason: resolved })

    const handleType = toHuman === true ? '请人工处理' : '请观察👀'
    const message = `${ resolved }，${ handleType }${ additionalMsg ? `\n${additionalMsg}` : ''}`
    return await this.transferWithMessage(chatId, userId, message, toHuman, options)
  }

  // 方式二：直接传入完整通知文案（供上层自定义拼接使用）
  public async transferWithMessage(
    chatId: string,
    userId: string,
    notifyMessage: string,
    toHuman: TransferMode = true,
    options?: NotifyOptions & { notifyFunction?: () => Promise<void>, imBotId?: string }
  ) {
    const chat = await this.chatDBClient.getById(chatId) as IChat
    if (typeof toHuman === 'boolean' && chat) {
      await this.chatDBClient.setHumanInvolvement(chatId, toHuman)
    } else {
      if (!chat) {
        const currentSender = await JuziAPI.getCustomerInfo(Config.setting.wechatConfig?.id as string, userId)
        await this.chatDBClient.create({
          id: chatId,
          round_ids: [],
          contact: {
            wx_id: userId,
            wx_name: currentSender ? currentSender.name : userId,
          },
          wx_id: Config.setting.wechatConfig?.id as string,
          created_at: new Date(),
          chat_state: await this.chatStateStoreClient.get(chatId)
        })
      }
    }

    // 包装通知
    let contactName = userId
    let notificationMessage: string
    const isPaid = chat?.chat_state?.state?.is_complete_payment ?? false
    if (chat && chat.contact && chat.contact.wx_name) {
      contactName = chat.contact.wx_name
    }
    notificationMessage = `${contactName} ${isPaid ? '💰' : ''}${notifyMessage}`

    if (toHuman === true) { notificationMessage += '\n[心碎]AI已关闭[心碎]' }
    logger.log({ chat_id: chatId }, '通知人工接入：', notificationMessage)
    if (toHuman === 'onlyNotify' && Config.setting.localTest) return

    // 兼容：如传入 notifyFunction，则优先生效
    if (options?.notifyFunction) {
      await options.notifyFunction()
      return
    }

    const opts = options?.imBotId ? { ...(options ?? {}), imBotId: options.imBotId } : options
    await this.notifier.notify(notificationMessage, opts)
  }
}
