import express from 'express'
import { init } from './bootstrap'
import { catchGlobalError } from 'model/server/server'
import { register } from 'lib/prometheus_client'
import logger from 'model/logger/logger'
import { ErrorComponent } from 'model/logger/errorTypes'
import { override } from '../config/override'
import { manifest } from '../config/manifest'
import { receiveMessageCount } from '../prometheus/client'
import { BigPlanner } from '../planner/daily_plan/big_planner'

async function main() {
  const ctx = await init()

  const app = express()
  app.use(express.json())

  app.get('/', (req, res) => {
    logger.log('Hello Client, this is Server!')
    res.send('ok')
  })

  app.post('/message', async (req, res) => {
    ctx.messageHandler.handle(req.body)

    receiveMessageCount.labels({ bot_id: (manifest.projectName || 'unknown') }).inc(1)
    res.send('ok')
  })

  app.post('/read_link', async(req, res) => {
    override.handleReadLink(req.body)
    res.send('ok')
  })

  app.post('/read_message', async(req, res) => {
    override.handleReadMessag(req.body)
    res.send('ok')
  })

  app.post('/new_customer', async(req, res) => {
    override.handleNewCustomer(req.body)
    res.send('ok')
  })

  app.post('/crm_event', async(req, res) => {
    override.handleCrmEvent(req.body)
    res.send('ok')
  })

  app.post('/ddm_event', async(req, res) => {
    override.handleDDMEvent(req.body)
    res.send('ok')
  })

  app.get('/metrics', async (req, res) => {
    res.status(200).set('Content-Type', register.contentType).send(await register.metrics())
  })

  app.post('/clear_cache', async(req, res) => {
    await override.clearCache(req.body.chat_id)
    res.status(200).send('ok')
  })

  app.post('/big_planner', async(req, res) => {
    BigPlanner.plan(req.body.chat_id)
    res.status(200).send('ok')
  })

  // 测试Sentry错误上报
  app.get('/test-sentry', async (req, res) => {
    const testChatId = req.query.chat_id as string || `test_${Date.now()}`

    // 测试各种错误组件类型
    logger.error({ chat_id: testChatId, component: ErrorComponent.MESSAGE_ROUTING }, '消息路由测试错误')
    logger.error({ chat_id: testChatId, component: ErrorComponent.AI_PLANNING }, 'AI规划失败', new Error('测试异常'))
    logger.error({ chat_id: testChatId, component: ErrorComponent.DATA_SERVICE }, '数据服务失败')
    logger.error({ chat_id: testChatId, component: ErrorComponent.EXTERNAL_API_CALL }, '外部API调用失败', new Error('网络超时'))
    logger.error({ chat_id: testChatId, component: ErrorComponent.AI_CONTEXT }, 'AI上下文构建失败')

    res.json({
      success: true,
      message: 'Sentry错误上报测试完成',
      chat_id: testChatId,
      hint: '请检查Sentry控制台查看错误记录'
    })
  })

  ctx.messageHandler.startWorker()

  app.listen(ctx.port, '0.0.0.0', () => {
    console.log(`Server is running on port ${ctx.port}`)
  })

  catchGlobalError()
}

main().catch((e) => {
  console.error(e)
})