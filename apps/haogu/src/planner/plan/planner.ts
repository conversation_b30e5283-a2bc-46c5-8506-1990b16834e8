import dayjs from 'dayjs'
import { Job, Queue } from 'bullmq'
import { ContextBuilder } from '../../workflow/context'
import { TaskManager } from 'service/task/task_manager'
import { TaskStatus } from 'service/task/type'
import { PrismaMongoClient } from 'model/mongodb/prisma'
import { SchedulePlanItem, TaskScheduler } from 'service/task/task_scheduler'
import logger from 'model/logger/logger'
import { ErrorComponent } from 'model/logger/errorTypes'
import { RedisCacheDB } from 'model/redis/redis_cache'
import { getBotId, getUserId } from 'config/chat_id'
import { RedisDB } from 'model/redis/redis'
import { getState } from 'service/llm/state'
import { chatHistoryServiceClient } from '../../config/instance/base_instance'
import { getVisualizedSopQueueName } from 'service/visualized_sop/visualized_sop_task_starter'
import { ISOPTask } from 'service/visualized_sop/visualized_sop_type'
import { ITask } from '../types'
import { taskWorker } from '../../config/instance/task_instance'
import { manifest } from '../../config/manifest'
import { TaskWorker } from 'service/task/task_worker'

interface IScheduleTask {
  task_id: string
  urgency_level: 'urgent' | 'normal'
  task_type: 'daily_greeting' | 'pre_class_reminder' | 'post_class_follow_up' | 'engagement_prompt' | 'value_delivery'
  scheduled_time: string // YYYY-MM-DD HH:MM:SS 或 "now"

  description: string // 任务描述
  chat_id: string
}

export class Planner {
  /**
   * 将任务转为定时发送的 SOP
   * @param chat_id
   * @param tasks
   */
  public static async taskSchedule(chat_id: string, tasks: ITask[]) {
    // context 拼装
    const contextBuilder = new ContextBuilder({ state: await getState(chat_id, getUserId(chat_id), '') })

    const currentTime = await contextBuilder.temporalInformation(chat_id)

    const tasksToSchedule = tasks
      .filter((task) => task.send_time === null) // 已经规划过的任务，不再进行二次规划
      .map((task) => {
        return {
          task_id: task.id.slice(-4),
          task_description: task.description
        }
      })

    // 获取 visualized sop 和 planner 消息队列
    const existing_schedule = await this.filterTasksByDate(chat_id, new Date(), new Date(Date.now() + 3 * 24 * 60 * 60 * 1000))

    const customerBehavior = await contextBuilder.customerBehavior(chat_id)
    const dialogHistory = await chatHistoryServiceClient.getDialogHistory(chat_id, 3, 10)

    const scheduledTask =  await TaskScheduler.scheduleTask({
      user_profile: `${customerBehavior}\n\n${  dialogHistory}`,
      current_time: currentTime,
      tasks_to_schedule: tasksToSchedule,
      existing_schedule: existing_schedule,
      chat_id
    })

    // 对 task Id 进行还原
    const idMap = tasks.reduce((map, task) => {
      map[task.id.slice(-4)] = task.id
      return map
    }, {} as Record<string, string>)


    // 假设 scheduledTask 是 [{ task_id: 'a1b2', ... }, ...]
    const scheduledTasks = scheduledTask
      .map((item) => {
        const fullTaskId = idMap[item.task_id]
        if (!fullTaskId) {
          logger.error({ chat_id: chat_id, component: ErrorComponent.AI_PLANNING }, `无效的任务ID: ${item.task_id}`)
          return null
        }
        return {
          ...item,
          task_id: fullTaskId // 还原完整 id
        }
      })
      .filter((task) => task !== null) as SchedulePlanItem[]

    // 更新发送时间 - 只处理有效的任务
    await Promise.allSettled(scheduledTasks.map(async (task) => {
      await TaskManager.updateTask(task.task_id, { send_time: task.scheduled_time })
    }))


    logger.log('规划任务时间', JSON.stringify(scheduledTasks, null, 4))
    return scheduledTasks
  }

  /**
   * 添加延迟任务到消息队列
   * @param chat_id
   * @param task_ids
   */
  public static async addDelayedTask(chat_id: string, task_ids: string[]) {
    const queue = new Queue<IScheduleTask>(this.getPlannerSOPQueueName(getBotId(chat_id)), {
      connection: RedisDB.getInstance()
    })

    const jobs: {name: string, data: any, opts: any}[] = []

    // 通过 task_id 查询出原始的 task
    for (const taskId of task_ids) {
      const task = await TaskManager.getTaskById(taskId)

      if (!task) continue
      if (task.status !== 'TODO') continue

      try {
        jobs.push({
          name: task.description,
          data: task,
          opts: { delay: new Date(task.send_time as string).getTime() - Date.now() }
        })
      } catch (e) {
        logger.error({chat_id: chat_id, component: ErrorComponent.AI_PLANNING }, '延迟任务时间计算失败', e)
      }
    }

    await queue.addBulk(jobs)
  }

  public static getPlannerSOPQueueName(botId:string) {
    return TaskWorker.getTaskQueueName(botId, manifest.projectName)
  }

  private static async listSOPByChatId(chatId: string): Promise<Job[]> {
    const queue = new Queue<IScheduleTask>(this.getPlannerSOPQueueName(getBotId(chatId)), {
      connection: RedisDB.getInstance()
    })

    const allJobs = await queue.getDelayed()
    return allJobs.filter((item) => item.data.chat_id === chatId)
  }

  public static async filterSOPByDate(chatId: string, startDate: Date, endDate: Date) {
    const sops = await queryExistSopByChatId(chatId)
    // 获取从开始日期到 endDate 期间的 job
    const filteredSOPs = sops.filter((item) => {
      const jobTime = new Date(item.delay + item.timestamp)
      return jobTime >= startDate && jobTime <= endDate
    })

    // redis 中取回对应的 title
    const sopValue = await new RedisCacheDB(`${manifest.projectName}:${getBotId(chatId)}:visualized_sop`).get()

    // 构建 SOP Map
    const sopMap = new Map()
    for (const sop of sopValue) {
      sopMap.set(sop.id, sop.title)
    }

    for (const sop of filteredSOPs) {
      if (!sopMap.has(sop.name)) {
        logger.error({ chat_id: chatId, component: ErrorComponent.AI_PLANNING }, `SOP映射缺失: ${sop.name}`)
        continue
      }
      sop.name = sopMap.get(sop.name)
    }

    return filteredSOPs
  }


  public static async filterTasksByDate(chatId: string, startDate: Date, endDate: Date) {
    const plannerTasks = await Planner.listSOPByChatId(chatId)

    const filteredPlannerSOPs = plannerTasks.filter((item) => {
      const jobTime = new Date(item.delay + item.timestamp)
      return jobTime >= startDate && jobTime <= endDate
    })

    const filteredSOPs = await Planner.filterSOPByDate(chatId, startDate, endDate)

    // 合并 以 时间排序
    const mergedSOPs = filteredSOPs.map((item) => {
      return {
        description: item.name,
        time: new Date(item.delay + item.timestamp)
      }
    }).concat(filteredPlannerSOPs.map((item) => {
      return {
        description: item.name,
        time: new Date(item.delay + item.timestamp)
      }
    })).sort((a, b) => a.time.getTime() - b.time.getTime())

    // 描述 + 时间
    return mergedSOPs.map((item) => {
      return {
        description: item.description,
        time: dayjs(item.time).format('YYYY-MM-DD HH:mm:ss')
      }
    })
  }


  static async executeImmediateTask(chat_id: string, scheduledTasks: SchedulePlanItem[]) {
    // 获取任务描述进行合并
    // 执行任务
    // 更新任务状态
    const immediateTasks = scheduledTasks.filter((item) => item.scheduled_time === 'now')

    const tasks = await PrismaMongoClient.getInstance().task.findMany({
      where: {
        chat_id,
        id: {
          in: immediateTasks.map((item) => item.task_id)
        }
      }
    })

    // 合并 Task 描述
    const taskDescription = tasks.filter((item) => item.status === 'TODO').map((item, index) => `${index + 1}. ${item.description}`).join('\n')

    // 更新任务状态
    await Promise.all(tasks.map((task) => TaskManager.updateStatus(task.id, TaskStatus.DONE)))


    if (taskDescription.trim() === '') {
      return
    }

    await taskWorker.processTask(chat_id, taskDescription)
  }
}

async function queryExistSopByChatId(chatId:string):Promise<Job[]> {
  const queue = new Queue<ISOPTask>(getVisualizedSopQueueName('haogu', getBotId(chatId)), {
    connection: RedisDB.getInstance()
  })
  const jobs =  await queue.getDelayed()

  return jobs.filter((item) => item.data.chatId == chatId)
}