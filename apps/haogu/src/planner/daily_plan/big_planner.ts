import { Planner } from '../plan/planner'
import dayjs from 'dayjs'
import { ITask, PlanOperations, PlanResponse } from '../types'
import { Config } from 'config'
import { DateHelper } from 'lib/date/date'
import { UUID } from 'lib/uuid/uuid'
import { CommonTaskType, SilentReAsk } from 'service/schedule/silent_requestion'
import { extractUserSlots, memoryStoreClient } from '../../config/instance/instance'
import { ContextBuilder } from '../../workflow/context'
import { StageManager } from '../stage/stage_manager'
import { getState } from 'service/llm/state'
import { getUserId } from 'config/chat_id'
import { MemoryRecall } from 'service/memory/memory_search'
import { DataService } from '../../workflow/helper/get_data'
import logger from 'model/logger/logger'
import { LLM } from 'lib/ai/llm/llm_model'
import { getPrompt } from 'service/agent/prompt'
import { TaskStatus } from 'service/task/type'
import { ICreateTask, TaskManager } from 'service/task/task_manager'
import { chatHistoryServiceClient, chatStateStoreClient } from '../../config/instance/base_instance'
import { IChattingFlag } from '../../config/manifest'
import { ErrorComponent } from 'model/logger/errorTypes'


export class BigPlanner {
  public static async addTasks(chat_id: string, courseStartTime: Date) {
    // 只能被添加一次
    if ((await chatStateStoreClient.getFlags<IChattingFlag>(chat_id)).is_add_big_plan_tasks) {
      return
    }

    // 从 上课日  到 上课日 + 6
    const now = new Date()
    const courseEndTime = dayjs(courseStartTime).add(6, 'day').toDate()
    let cursor = dayjs(courseStartTime).startOf('day')

    while (cursor.isBefore(courseEndTime)) {
      const runAt = cursor.toDate()

      // 计算延迟时间（毫秒）
      const delay = DateHelper.diff(now, runAt, 'millisecond')

      if (delay > 0) {
        await SilentReAsk.schedule(
          CommonTaskType.BigPlan,
          chat_id,
          delay,   // 这里用毫秒延迟
          undefined,
          {
            independent: true,
            chatActivityChecker: async (chat_id: string) => {
              // 检查最近3分钟内是否有非营销的聊天记录
              return await chatHistoryServiceClient.isLastMessageWithDuration(chat_id, 3, 'minute')
            }
          }
        )
      }

      // 下一天
      cursor = cursor.add(1, 'day')
    }

    await chatStateStoreClient.update(chat_id, {
      state: {
        is_add_big_plan_tasks: true
      }
    })
  }

  private static async buildContext(chat_id: string) {
    // 实时一下补全 memory 和 userSlots
    if (!Config.setting.localTest) {
      await memoryStoreClient.extractMemoriesAndUserSlots(chat_id, UUID.v4(), extractUserSlots, true)
    }

    const contextBuilder = new ContextBuilder({ state: await getState(chat_id, getUserId(chat_id), '') })

    const userSlots = await contextBuilder.customerPortrait(chat_id)
    const memory = (await MemoryRecall.getRecentMemory(chat_id, 5)).join('\n')
    const userBehavior = await contextBuilder.customerBehavior(chat_id)
    const silentAnalyze = ''

    const timeInfo = await DataService.getCurrentTime(chat_id)
    let currentTime = await contextBuilder.temporalInformation(chat_id)

    if (timeInfo.day < 1) {
      currentTime += `
当前时间是上课前 ${(timeInfo.day - 1) * -1}天`
    }

    //阶段信息管理
    const stageManager = new StageManager()
    const stagePrompt = await stageManager.getStageInformation(chat_id)
    currentTime += `\n${stagePrompt}`

    // 今天 0 点
    const startOfToday = dayjs().startOf('day').toDate()

    // 今天晚上 0 点（也就是明天 0 点）
    const endOfToday = dayjs().endOf('day').toDate()

    const todayTasks = await Planner.filterTasksByDate(chat_id, startOfToday, endOfToday)

    return {
      user_slots: userSlots,
      memory,
      user_behavior: userBehavior,
      silent_analyze: silentAnalyze,
      current_time: currentTime,
      existing_task: todayTasks.map((task, index) => ({ id: `${index + 1}`, ...task }))
    }
  }

  public static async plan(chat_id: string) {
    const planResult = await this.generatePlan(chat_id)
    if (!planResult) {
      return
    }

    const { planResponse, context } =  planResult

    // 获取当前活跃任务和今日任务用于匹配
    const currentTasks = await TaskManager.getScheduledTasks(chat_id)

    const todayTasks = context.existing_task

    const { plans, think } = planResponse

    try {
      await this.executePlanOperations(chat_id, plans, currentTasks, todayTasks, think)
    } catch (error) {
      logger.error({ chat_id: chat_id, component: ErrorComponent.AI_PLANNING }, 'BigPlanner 执行任务操作失败', error)
    }
  }

  /**
   * 执行计划操作：处理新增、更新、删除、合并任务
   * @param chat_id 聊天ID
   * @param plans 计划操作
   * @param currentTasks 当前活跃任务
   * @param todayTasks 今日任务
   * @param think
   */
  private static async executePlanOperations(
    chat_id: string,
    plans: PlanOperations,
    currentTasks: ITask[],
    todayTasks: { id: string, description: string, time: string }[],
    think: string
  ) {
    // 1. 处理新增任务
    if (plans.toAdd && plans.toAdd.length > 0) {
      const tasksToAdd: ICreateTask[] = []
      const sendTimes: (string | undefined)[] = []

      let lastTs: number | null = null
      plans.toAdd.forEach((task) => {
        if (task && typeof task === 'object' && task.content && task.send_time) {
          const send_time = dayjs(`${dayjs().format('YYYY-MM-DD')} ${task.send_time}`)

          if (!send_time.isValid()) return

          const ts = send_time.valueOf()

          // 如果遇到下降（跨天），直接停止后续处理
          if (lastTs !== null && ts < lastTs) return

          tasksToAdd.push({
            description: task.content,
            sendTime: send_time.format(),
            type: task.type
          })
          sendTimes.push(send_time.format())

          lastTs = ts
        }
      })

      // 创建任务
      const createResult = await TaskManager.createTasks(
        chat_id,
        tasksToAdd,
        think, // 默认目标
        undefined // 不指定 round_id
      )

      // 如果有 send_time，更新任务的 send_time 字段
      if (createResult && createResult.tasks) {
        for (let i = 0; i < sendTimes.length && i < createResult.tasks.length; i++) {
          // 添加任务到任务队列
          await Planner.addDelayedTask(chat_id, [createResult.tasks[i].id])
        }
      }

      // logger.log({ chat_id }, `BigPlanner 新增任务: ${tasksToAdd.length} 个`)
    }

    // 2. 处理更新任务
    if (plans.toUpdate && plans.toUpdate.length > 0) {
      // 今天 0 点
      const startOfToday = dayjs().startOf('day').toDate()
      // 今天晚上 0 点（也就是明天 0 点）
      const endOfToday = dayjs().endOf('day').toDate()

      const sopJobs = await Planner.filterSOPByDate(chat_id, startOfToday, endOfToday)

      for (const updateItem of plans.toUpdate) {
        const taskIndex = parseInt(updateItem.id, 10) - 1
        if (taskIndex >= 0 && taskIndex < todayTasks.length) {
          const todayTask = todayTasks[taskIndex]
          // 在当前活跃任务中通过描述匹配找到对应任务
          const matchedTask = currentTasks.find((task) =>
            task.description === todayTask.description
          )

          if (matchedTask) {
            await TaskManager.updateTask(matchedTask.id, {
              description: updateItem.content,
              type: updateItem.type
            })
            // logger.log({ chat_id }, `BigPlanner 更新任务: ${matchedTask.id} -> ${updateItem.content}`)
          } else {
            // SOP 任务的转换
            await TaskManager.createTasks(chat_id, [
              {
                description: updateItem.content,
                sendTime: todayTask.time,
                type: updateItem.type
              }
            ], think)

            // 需要去 cancel 对应的 SOP
            sopJobs.find((sop) => sop.name === todayTask.description)?.remove()
          }
        }
      }
    }

    // 3. 处理删除任务
    if (plans.toRemove && plans.toRemove.length > 0) {
      for (const removeId of plans.toRemove) {
        const taskIndex = parseInt(removeId, 10) - 1 // 转换为 0 基索引
        if (taskIndex >= 0 && taskIndex < todayTasks.length) {
          const todayTask = todayTasks[taskIndex]
          // 在当前活跃任务中通过描述匹配找到对应任务
          const matchedTask = currentTasks.find((task) =>
            task.description === todayTask.description ||
            task.description.includes(todayTask.description) ||
            todayTask.description.includes(task.description)
          )

          if (matchedTask) {
            await TaskManager.updateStatus(matchedTask.id, TaskStatus.CANCELED)
            // logger.log({ chat_id }, `BigPlanner 删除任务: ${matchedTask.id}`)
          }
        }
      }
    }

    // 4. 处理合并任务
    if (plans.toMerge && plans.toMerge.length > 0) {
      for (const mergeItem of plans.toMerge) {
        const targetIndex = parseInt(mergeItem.into, 10) - 1 // 转换为 0 基索引
        if (targetIndex >= 0 && targetIndex < todayTasks.length) {
          const targetTodayTask = todayTasks[targetIndex]
          // 找到目标任务
          const targetTask = currentTasks.find((task) =>
            task.description === targetTodayTask.description ||
            task.description.includes(targetTodayTask.description) ||
            targetTodayTask.description.includes(task.description)
          )

          if (targetTask) {
            // 更新目标任务的描述为合并后的内容
            await TaskManager.updateTask(targetTask.id, {
              description: mergeItem.mergedContent,
              type: mergeItem.type,
              send_time: targetTask.send_time ?? undefined
            })

            // 删除源任务
            for (const fromId of mergeItem.from) {
              const fromIndex = parseInt(fromId, 10) - 1
              if (fromIndex >= 0 && fromIndex < todayTasks.length) {
                const fromTodayTask = todayTasks[fromIndex]
                const fromTask = currentTasks.find((task) =>
                  task.description === fromTodayTask.description ||
                  task.description.includes(fromTodayTask.description) ||
                  fromTodayTask.description.includes(task.description)
                )

                if (fromTask && fromTask.id !== targetTask.id) {
                  await TaskManager.updateStatus(fromTask.id, TaskStatus.CANCELED)
                }
              }
            }

            // logger.log({ chat_id }, `BigPlanner 合并任务: ${mergeItem.from.join(',')} -> ${mergeItem.into}`)
          }
        }
      }
    }
  }

  public static async generatePlan(chat_id: string) {
    const prompt = await getPrompt('hrhg-bigplanner')

    const context = await this.buildContext(chat_id)

    // 解析 LLM 返回的结果
    let planResponse: PlanResponse
    try {
      const result = await LLM.predict(
        prompt, {
          model: 'gpt-5',
          maxTokens: 4096,
          responseJSON: true,
          promptName: 'planner',
          meta: { chat_id }
        }, context)

      planResponse = result as PlanResponse
      if (!planResponse || !planResponse.plans) {
        throw new Error('Invalid plan response structure')
      }
    } catch (error) {
      logger.error({ chat_id: chat_id, component: ErrorComponent.AI_PLANNING }, 'BigPlanner 解析 JSON 失败', error)
      return
    }

    return {
      planResponse,
      context
    }
  }
}