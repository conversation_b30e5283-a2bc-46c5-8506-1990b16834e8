import logger from 'model/logger/logger'
import { ErrorComponent } from 'model/logger/errorTypes'
import { chatStateStoreClient } from '../config/instance/base_instance'
import { checkRobotDetection } from 'service/agent/utils'
import { commonMessageSender } from '../config/instance/send_message_instance'
import { DateHelper } from 'lib/date/date'
import { DataService } from './helper/get_data'
import { getPrompt } from 'service/agent/prompt'
import { humanTransfer } from '../config/instance/instance'
import { IWorkflowState } from 'service/llm/state'
import { LLM } from 'lib/ai/llm/llm_model'
import { RegexHelper } from 'lib/regex/regex'
import { Node } from 'service/agent/workflow'
import { HumanTransfer } from './helper/human_transfer'
import { HumanTransferType } from 'service/human_transfer/human_transfer'
import { sleep } from 'lib/schedule/schedule'

export class Router {
  /**
   * 根据客户消息进行路由，特别注意这里的路由要写的 特定情况才能跳转，不能太通用，不然容易路由到错误的节点
   * 返回 End, 表示不执行任何节点逻辑
   * @param state
   */
  public static async route(state: IWorkflowState): Promise<Node> {
    const chatId = state.chat_id
    const userId = state.user_id
    const roundId = state.round_id
    const userMessage = state.userMessage
    const currentTime = await DataService.getCurrentTime(chatId)
    const day = currentTime.day
    if (!userMessage) return Node.Dummy

    // 刷新到完课状态
    if (0 < day && day < 5 && DateHelper.isTimeAfter(currentTime.time, '18:50:00')) {
      // await DataService.isAttendCourse(chatId, day)
      await DataService.isCompletedCourse(chatId, day)
    }

    // 废话过滤
    const isChatter = RegexHelper.filterChatter(userMessage)
    const beforeCourse3 = await DataService.isInCourseTimeLine(chatId, 'beforeCourse', 3)
    if (isChatter && beforeCourse3) return Node.DummyEnd

    // 客户识别AI检查
    const isRobotDetection = await checkRobotDetection(chatStateStoreClient, humanTransfer, chatId, roundId, userId, userMessage)
    if (isRobotDetection) return Node.DummyEnd

    //发送约课礼
    // await this.sendClassBookingGift(userMessage, chatId)

    // 意图分类路由
    return await this.routeByCategory(userMessage, chatId, userId, roundId)
  }

  // 意图分类路由
  private static async routeByCategory(userMessage: string, chat_id: string, user_id: string, round_id: string): Promise<Node> {
    const category = await Router.classify(userMessage, chat_id, round_id)
    if (category === 7) {  // 看课问题
      // await HumanTransfer.transfer(chat_id, user_id, HumanTransferType.ProblemSolving, 'onlyNotify', `客户：${userMessage}`)
      // const phoneNumber = await chatDBClient.getPhone(chat_id)
      // const message = phoneNumber ? `用这个手机号登录哈，老师帮你开权限了\n${phoneNumber}` : '麻烦提供一下手机号哈，这边后台帮你开权限'
      // await commonMessageSender.sendText(chat_id, { text: message, description: '客户无法看课，给解决方案' })
      return Node.Dummy
    } else if (category === 8) {  // 异常问题
      await HumanTransfer.transfer(chat_id, user_id, HumanTransferType.ProblemSolving, 'onlyNotify', `客户：${userMessage}`)
    } else if (category === 9) {  // 人工处理
      await HumanTransfer.transfer(chat_id, user_id, HumanTransferType.ProblemSolving, true, `客户：${userMessage}`)
      return Node.DummyEnd
    }
    return Node.Dummy  // 正常对话
  }

  public static async classify(customerMessage: string, chat_id: string, round_id: string) {
    const routerPrompt = await getPrompt('router')
    const routingNodes = '6. 图片内容：客户发送图片URL'

    try {
      const output = await LLM.predict(
        routerPrompt, {
          responseJSON: true,
          meta: {
            promptName: 'router',
            chat_id: chat_id,
            round_id: round_id,
          } }, {
          routingNodes: routingNodes,
          customerMessage: customerMessage,
        })
      const answer: number = output.answer

      return answer || 0
    } catch (error) {
      logger.error({ chat_id, component: ErrorComponent.CONVERSATION_FLOW }, 'Router 解析 JSON 失败', error)

      return  0
    }

  }

  private static async sendClassBookingGift(customerMessage: string, chat_id: string) {
    if (customerMessage !== '约课礼') {
      return
    }

    const sourceIdMap = {
      1:'405683',
      2:'405684',
      3:'313225',
      4:'267428',
      5:'313386',
      6:'267946'
    }

    const currentTime = await DataService.getCurrentTime(chat_id)

    if (currentTime.day < 1 || currentTime.day > 6) {
      return
    }

    await sleep(3000)
    await commonMessageSender.sendMaterial(chat_id, { sourceId: sourceIdMap[currentTime.day] })

  }


}
