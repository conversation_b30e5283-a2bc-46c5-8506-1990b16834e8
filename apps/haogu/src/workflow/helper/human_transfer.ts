import logger from 'model/logger/logger'
import { ErrorComponent } from 'model/logger/errorTypes'
import { DataService } from './get_data'
import { eventTrackClient } from '../../config/instance/event_track_instance'
import { humanTransferClient } from '../../config/instance/human_transfer_instance'
import { IEventType } from 'model/logger/data_driven'

export class HumanTransfer {
  public static async transfer(chatId: string, userId: string, transferMessage: string, toHuman: boolean | 'onlyNotify' = true, additionalMsg?: string) {
    if (userId === 'null') {
      logger.error({ chat_id: chatId, component: ErrorComponent.HUMAN_SERVICE }, `[HumanTransfer] userId is null: ${transferMessage}`)
      return
    }
    eventTrackClient.track(chatId, IEventType.TransferToManual, { reason: transferMessage })
    const courseNo = DataService.getCurrentCourseNo() // await DataService.getCourseNoByChatId(chatId) ?? 2025
    const handleType = toHuman === true ? '请人工处理' : '请观察👀'
    const message = `（${ courseNo }）${ transferMessage }，${ handleType }${ additionalMsg ? `\n${ additionalMsg }` : ''}`
    const imBotId = '1688858254705213'
    const groupId = 'R:10964085377574680'
    return await humanTransferClient.transferWithMessage(chatId, userId, message, toHuman, { groupId, imBotId })
  }
}
