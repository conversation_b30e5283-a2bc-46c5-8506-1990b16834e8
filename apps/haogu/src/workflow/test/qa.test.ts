import { UUID } from 'lib/uuid/uuid'
import { Workflow } from '../workflow' // 导入下方提供的 JSON 数据
import { when } from 'jest-when'
import { DataService } from '../helper/get_data'
import { Config } from 'config'
import { chatHistoryServiceClient } from '../../config/instance/base_instance'
import { FileHelper } from 'lib/file'
import path from 'path'
import fs from 'fs'
import { catchError } from 'lib/error/catchError'
import dayjs from 'dayjs'

const collectedChatIds: any[] = [] // 用来收集所有生成的 chat_id

describe('AI Bot Automated Test Suite', function () {
  beforeAll(() => {
    Config.setting.projectName = 'haogu'
  })

  beforeEach(() => {
    // 重置 spy 的实现，再设置默认返回（未指定 chatId 时使用）
    getCurrentTimeMock.mockReset()
    getCurrentTimeMock.mockResolvedValue({ day: 0, time: '00:00:00' })

    getCourseStartTimeMock.mockReset()
    getCourseStartTimeMock.mockResolvedValue(dayjs().add(1, 'day').hour(19).minute(20).second(0).toDate())
  })

  const file = fs.readFileSync(path.join(process.cwd(), 'dev', 'test_suite(1).json'), 'utf8')
  const TestSuite = JSON.parse(file)

  const getCurrentTimeMock = jest
    .spyOn(DataService, 'getCurrentTime')
    .mockResolvedValue({ day: 0, time: '00:00:00' })

  const getCourseStartTimeMock = jest
    .spyOn(DataService, 'getCourseStartTimeByChatId')
    .mockResolvedValue(dayjs().add(1, 'day').hour(19).minute(20).second(0).toDate())

  for (const categoryName in TestSuite) {
    const testCases = TestSuite[categoryName]

    describe(`Category: ${categoryName}`, function () {
      for (const testCase of testCases) {
        const isMultiTurn = 'turns' in testCase
        const testName = isMultiTurn ? testCase.scenario : testCase.query_text

        const executeTest = async (timeMockLabel, categoryName) => {
          // 先生成 userId 和 chatId
          const userId = UUID.short()
          const test_chat_id = `test${timeMockLabel ?? ''}_${userId}`

          if (timeMockLabel === 'Day0') {
            when(getCurrentTimeMock)
              .calledWith(test_chat_id)
              .mockResolvedValue({ day: 0, time: '08:00:00' })
            when(getCourseStartTimeMock)
              .calledWith(test_chat_id)
              .mockResolvedValue(dayjs().add(1, 'day').hour(19).minute(20).second(0).toDate())
          } else if (timeMockLabel === 'Day6') {
            when(getCurrentTimeMock)
              .calledWith(test_chat_id)
              .mockResolvedValue({ day: 6, time: '23:00:00' })
            when(getCourseStartTimeMock)
              .calledWith(test_chat_id)
              .mockResolvedValue(dayjs().subtract(6, 'day').hour(19).minute(20).second(0).toDate())
          }

          // 2. 执行对话
          if (isMultiTurn) {
            for (const turn of testCase.turns) {
              const input = turn.formatted_input || turn.query_text
              await Workflow.step(test_chat_id, userId, input)
            }
          } else {
            const input = testCase.formatted_input || testCase.query_text
            await Workflow.step(test_chat_id, userId, input)
          }

          // 3. 收集 chat_id
          collectedChatIds.push({
            name: testName,
            chat_id: test_chat_id,
            time: timeMockLabel,
            categoryName: categoryName
          })

          console.log(
            `Executed Test: ${testName}, Chat ID: ${test_chat_id}, TimeContext: ${timeMockLabel}`
          )
        }

        it.concurrent(`should handle: ${testName}`, async () => {
          const runs = testCase.time_sensitive ? ['Day0', 'Day6'] : [null]
          await Promise.all(runs.map((label) => executeTest(label, categoryName)))
        }, 300000)
      }
    })
  }

  afterAll(async () => {
    console.log('=== All Collected Chat IDs ===')
    console.log(JSON.stringify(collectedChatIds, null, 2))

    // 分组测试结果
    const timeInsensitiveTests = collectedChatIds.filter((item) => item.time === null)
    const day0Tests = collectedChatIds.filter((item) => item.time === 'Day0')
    const day6Tests = collectedChatIds.filter((item) => item.time === 'Day6')

    // 定义文件路径
    const timeInsensitiveFile = path.join(process.cwd(), 'dev', '时间不敏感问题.txt')
    const day0File = path.join(process.cwd(), 'dev', '时间敏感类问题Day0.txt')
    const day6File = path.join(process.cwd(), 'dev', '时间敏感类问题Day6.txt')

    // 处理时间不敏感问题
    if (timeInsensitiveTests.length > 0) {
      await catchError(FileHelper.removeFile(timeInsensitiveFile))
      await FileHelper.touchFile(timeInsensitiveFile)

      for (const collectedChatId of timeInsensitiveTests) {
        const chat_history = await chatHistoryServiceClient.getFormatChatHistoryByChatId(collectedChatId.chat_id)
        console.log(chat_history)
        await FileHelper.appendFile(timeInsensitiveFile, `问题分类：${collectedChatId.categoryName}\n${chat_history}\nhttp://116.62.164.13:3000/haogu/user/chat/${collectedChatId.chat_id}\n\n`)
      }
    }

    // 处理时间敏感类问题Day0
    if (day0Tests.length > 0) {
      await catchError(FileHelper.removeFile(day0File))
      await FileHelper.touchFile(day0File)

      for (const collectedChatId of day0Tests) {
        const chat_history = await chatHistoryServiceClient.getFormatChatHistoryByChatId(collectedChatId.chat_id)
        console.log(chat_history)
        await FileHelper.appendFile(day0File, `问题分类：${collectedChatId.categoryName}\n${chat_history}\nhttp://116.62.164.13:3000/haogu/user/chat/${collectedChatId.chat_id}\n\n`)
      }
    }

    // 处理时间敏感类问题Day6
    if (day6Tests.length > 0) {
      await catchError(FileHelper.removeFile(day6File))
      await FileHelper.touchFile(day6File)

      for (const collectedChatId of day6Tests) {
        const chat_history = await chatHistoryServiceClient.getFormatChatHistoryByChatId(collectedChatId.chat_id)
        console.log(chat_history)
        await FileHelper.appendFile(day6File, `问题分类：${collectedChatId.categoryName}\n${chat_history}\nhttp://116.62.164.13:3000/haogu/user/chat/${collectedChatId.chat_id}\n\n`)
      }
    }

    console.log('\n=== 测试结果已分别输出到以下文件 ===')
    console.log(`时间不敏感问题: ${timeInsensitiveTests.length} 个测试 -> ${timeInsensitiveFile}`)
    console.log(`时间敏感类问题Day0: ${day0Tests.length} 个测试 -> ${day0File}`)
    console.log(`时间敏感类问题Day6: ${day6Tests.length} 个测试 -> ${day6File}`)
  })
})